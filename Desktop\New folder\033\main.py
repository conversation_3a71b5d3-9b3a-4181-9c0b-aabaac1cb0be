from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
import random
import os

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///participants.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.secret_key = 'your-secret-key-here'  # Change this in production

db = SQLAlchemy(app)

class Participant(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    
    def __repr__(self):
        return f'<Participant {self.name}>'

# Create database tables
with app.app_context():
    db.create_all()

@app.route('/')
def index():
    participants = Participant.query.all()
    count = len(participants)
    return render_template('index.html', participants=participants, count=count)

@app.route('/add', methods=['POST'])
def add():
    name = request.form.get('name', '').strip()
    if name:
        # Check if participant already exists
        existing = Participant.query.filter_by(name=name).first()
        if existing:
            flash(f'المشارك "{name}" موجود بالفعل!', 'warning')
        else:
            new_participant = Participant(name=name)
            db.session.add(new_participant)
            db.session.commit()
            flash(f'تم إضافة المشارك "{name}" بنجاح!', 'success')
    else:
        flash('يرجى إدخال اسم صحيح!', 'error')
    
    return redirect(url_for('index'))

@app.route('/delete/<int:participant_id>')
def delete_participant(participant_id):
    participant = Participant.query.get_or_404(participant_id)
    name = participant.name
    db.session.delete(participant)
    db.session.commit()
    flash(f'تم حذف المشارك "{name}"!', 'info')
    return redirect(url_for('index'))

@app.route('/clear_all')
def clear_all():
    Participant.query.delete()
    db.session.commit()
    flash('تم حذف جميع المشاركين!', 'info')
    return redirect(url_for('index'))

@app.route('/draw', methods=['POST'])
def draw():
    try:
        winners_number = int(request.form.get('winners_number', 0))
        participants = Participant.query.all()
        
        if winners_number <= 0:
            flash('يرجى إدخال عدد صحيح من الفائزين!', 'error')
            return redirect(url_for('index'))
        
        if winners_number > len(participants):
            flash(f'عدد الفائزين ({winners_number}) أكبر من عدد المشاركين ({len(participants)})!', 'error')
            return redirect(url_for('index'))
        
        if len(participants) == 0:
            flash('لا يوجد مشاركين للسحب عليهم!', 'error')
            return redirect(url_for('index'))
        
        # Randomly select winners
        winners = random.sample(participants, winners_number)
        
        return render_template('winners.html', winners=winners, total_participants=len(participants))
    
    except ValueError:
        flash('يرجى إدخال رقم صحيح!', 'error')
        return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5030)
