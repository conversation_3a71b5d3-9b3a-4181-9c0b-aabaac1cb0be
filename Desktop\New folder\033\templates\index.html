<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج السحب الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 20px 20px 0 0;
        }
        
        .participant-card {
            background: white;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 15px;
            animation: slideIn 0.5s ease-out;
        }
        
        .participant-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .add-form {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .draw-section {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stats-card {
            background: linear-gradient(45deg, #a8edea, #fed6e3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .participant-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }
        
        .delete-btn {
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            transition: all 0.3s ease;
        }
        
        .delete-btn:hover {
            background: #ff3742;
            transform: scale(1.1);
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.9);
        }
        
        .form-control:focus {
            border-color: #4ecdc4;
            box-shadow: 0 0 0 0.2rem rgba(78, 205, 196, 0.25);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-trophy"></i> برنامج السحب الإلكتروني</h1>
                <p class="mb-0">أضف المشاركين واختر الفائزين بطريقة عشوائية</p>
            </div>
            
            <div class="p-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'info' if category == 'info' else 'success' }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- Statistics -->
                <div class="stats-card">
                    <h3><i class="fas fa-users"></i> عدد المشاركين الحالي: {{ count }}</h3>
                </div>
                
                <!-- Add Participant Form -->
                <div class="add-form">
                    <h4 class="text-white mb-3"><i class="fas fa-user-plus"></i> إضافة مشارك جديد</h4>
                    <form method="POST" action="{{ url_for('add') }}" class="row g-3">
                        <div class="col-md-8">
                            <input type="text" class="form-control form-control-lg" name="name" placeholder="اسم المشارك" required>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-success btn-custom w-100">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Participants List -->
                {% if participants %}
                <div class="row">
                    <div class="col-md-8">
                        <h4><i class="fas fa-list"></i> قائمة المشاركين</h4>
                        <div class="row">
                            {% for participant in participants %}
                            <div class="col-md-6 mb-3">
                                <div class="participant-card card">
                                    <div class="card-body d-flex justify-content-between align-items-center">
                                        <span class="participant-name">{{ participant.name }}</span>
                                        <a href="{{ url_for('delete_participant', participant_id=participant.id) }}" 
                                           class="delete-btn" 
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المشارك؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        {% if count > 0 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('clear_all') }}" 
                               class="btn btn-outline-danger btn-custom"
                               onclick="return confirm('هل أنت متأكد من حذف جميع المشاركين؟')">
                                <i class="fas fa-trash-alt"></i> حذف الكل
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Draw Section -->
                    <div class="col-md-4">
                        <div class="draw-section">
                            <h4><i class="fas fa-dice"></i> إجراء السحب</h4>
                            <form method="POST" action="{{ url_for('draw') }}">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">عدد الفائزين:</label>
                                    <input type="number" class="form-control form-control-lg text-center" 
                                           name="winners_number" min="1" max="{{ count }}" required>
                                </div>
                                <button type="submit" class="btn btn-warning btn-custom btn-lg">
                                    <i class="fas fa-magic"></i> ابدأ السحب
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-5x text-muted mb-3"></i>
                    <h3 class="text-muted">لا يوجد مشاركين حتى الآن</h3>
                    <p class="text-muted">أضف بعض المشاركين لبدء السحب</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
