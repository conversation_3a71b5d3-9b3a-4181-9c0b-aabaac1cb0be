<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج السحب - الفائزون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1000px;
            min-height: 80vh;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: shine 3s linear infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .winner-card {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px 0;
            transform: scale(0);
            opacity: 0;
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
        }
        
        .winner-card.show {
            transform: scale(1);
            opacity: 1;
            animation: bounceIn 0.8s ease-out;
        }
        
        @keyframes bounceIn {
            0% {
                transform: scale(0.3);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .winner-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }
        
        .winner-card:hover::before {
            left: 100%;
        }
        
        .winner-number {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            margin: 0 auto 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .winner-name {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ff6b6b;
            animation: confetti-fall 3s linear infinite;
        }
        
        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 15px 40px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            font-size: 1.1em;
        }
        
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .celebration-text {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .stats-info {
            background: linear-gradient(45deg, #a8edea, #fed6e3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .trophy-icon {
            font-size: 3em;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: rotate 2s ease-in-out infinite;
        }
        
        @keyframes rotate {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-10deg); }
            75% { transform: rotate(10deg); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1 class="celebration-text">
                    <i class="fas fa-trophy trophy-icon"></i><br>
                    🎉 مبروك للفائزين! 🎉
                </h1>
                <p class="mb-0">نتائج السحب العشوائي</p>
            </div>
            
            <div class="p-4">
                <!-- Statistics -->
                <div class="stats-info">
                    <h4>
                        <i class="fas fa-chart-bar"></i>
                        تم اختيار {{ winners|length }} فائز من أصل {{ total_participants }} مشارك
                    </h4>
                </div>
                
                <!-- Winners List -->
                <div class="winners-container">
                    {% for winner in winners %}
                    <div class="winner-card" data-winner-index="{{ loop.index }}">
                        <div class="card-body text-center p-4">
                            <div class="winner-number">{{ loop.index }}</div>
                            <div class="winner-name">{{ winner.name }}</div>
                            <div class="mt-3">
                                <i class="fas fa-medal text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Action Buttons -->
                <div class="text-center mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-primary btn-custom me-3">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                    <button onclick="window.print()" class="btn btn-success btn-custom">
                        <i class="fas fa-print"></i> طباعة النتائج
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confetti Animation -->
    <div id="confetti-container"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show winners one by one with animation
        document.addEventListener('DOMContentLoaded', function() {
            const winnerCards = document.querySelectorAll('.winner-card');
            
            winnerCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('show');
                    // Play celebration sound effect (if available)
                    playWinnerSound();
                }, (index + 1) * 1000);
            });
            
            // Create confetti effect
            createConfetti();
        });
        
        function playWinnerSound() {
            // You can add audio files here
            // const audio = new Audio('path/to/celebration-sound.mp3');
            // audio.play().catch(e => console.log('Audio play failed:', e));
        }
        
        function createConfetti() {
            const confettiContainer = document.getElementById('confetti-container');
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.animationDelay = Math.random() * 3 + 's';
                    confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    confettiContainer.appendChild(confetti);
                    
                    // Remove confetti after animation
                    setTimeout(() => {
                        confetti.remove();
                    }, 5000);
                }, i * 100);
            }
        }
        
        // Add print styles
        const printStyles = `
            @media print {
                body { background: white !important; }
                .main-container { box-shadow: none !important; }
                .btn-custom { display: none !important; }
                .confetti { display: none !important; }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
